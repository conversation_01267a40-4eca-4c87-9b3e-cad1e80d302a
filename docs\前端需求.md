
### **《基础数据安全应用平台》前端开发需求文档 V1.0**



#### **1. 总体设计原则与风格指南**



**1.1 设计哲学**

- **信息主导 (Information-Centric):** 界面为数据服务，所有设计元素都应旨在更清晰、更高效地呈现信息，避免不必要的装饰干扰。
- **态势感知 (Situational Awareness):** 营造一种运筹帷幄的“指挥中心”氛围，让用户感觉一切尽在掌控。
- **流畅交互 (Fluid Interaction):** 动画和过渡效果需流畅、有意义，用于引导用户注意力，并提供操作的即时反馈。

**1.2 视觉风格指南 (Style Guide)**

- **主题与色板 (Theme & Color Palette):**
  - **主背景：** 深邃的藏蓝色 (`#021028`) 或深空黑，可带有微妙的星空、网格或电路板纹理。
  - **主容器/面板：** 半透明的深蓝色 (`rgba(5, 25, 60, 0.8)`), 带有明亮的青色 (`#00E5FF`) 或蓝色 (`#0A72FF`) 边框和几何角标装饰。
  - **主色调 (Accent Color):** 科技蓝 (`#0A72FF`)，用于可交互元素、图标和标题。
  - **高亮/告警色：** 鲜明的橙黄色 (`#FFB400`) 或红色 (`#FF4D4F`)，用于告警、关键数据和紧急按钮。
  - **图表色系：** 采用一组高饱和度的渐变色，如青色、蓝色、紫色、橙色，以在深色背景上突出显示。
- **字体 (Typography):**
  - **主字体：** 使用无衬线的、具有科技感的字体，如 `PangMenZhengDao`, `YouSheBiaoTiHei` (用于标题)，或 `Roboto`, `Source Han Sans` (用于正文)。
  - **标题：** 字体较大，常带有外发光或阴影效果。
  - **正文：** 清晰易读，颜色为淡蓝色 (`#A6C8FF`) 或白色，与深色背景形成对比。
- **图标 (Iconography):**
  - 采用简约、线性的科技风格图标，颜色为上述的主色调或白色。
  - 关键图标（如告警）可带有呼吸灯或闪烁的动态效果。
- **布局 (Layout):**
  - 采用模块化栅格布局，各个信息面板（“数据视窗”）分布在主视觉区域（如地图）周围。
  - 元素之间保持足够的间距，避免信息过载。



#### **2. 全局布局与核心组件**



**2.1 全局布局 (Global Layout)**

- **顶部信息栏 (Header Bar):**
  - **左侧：** 平台Logo和系统名称《基础数据安全应用平台》。
  - **中间：** 当前系统时间，实时跳动。
  - **右侧：** 告警通知入口（带角标）、用户头像、姓名及退出按钮。
- **左侧导航菜单 (Side Navigation):**
  - 可收缩式设计，默认展开。包含“任务看板”、“情报中心”、“统计报表”、“系统管理”等一级菜单入口。
  - 选中项有明显的光效或色块高亮。
- **主内容区 (Main Content Area):**
  - 占据屏幕绝大部分空间，用于承载各个模块的核心界面。

**2.2 核心可复用组件 (Core Components)**

- **数据视窗/面板 (Data Panel):** 所有信息图表和表单的容器。统一风格：半透明背景、发光边框、带标题。
- **主按钮 (Primary Button):** 科技蓝背景，鼠标悬浮时有光晕扩散效果。
- **表单元素 (Form Elements):**
  - **输入框/下拉菜单：** 无填充背景，只有底边框线。获得焦点时，边框线变亮并显示动效。
  - **日期/时间选择器：** 采用与整体风格匹配的深色主题。
- **模态框/弹窗 (Modal):**
  - 从屏幕中央淡入并放大，背景有径向渐变光效。
  - 用于显示审批详情、告警信息等。
- **数据表格 (Data Table):**
  - 深色主题，斑马条纹可采用不同深度的蓝色。
  - 表头有固定背景色，行数据有悬浮高亮效果。操作列的按钮（如“详情”）使用图标+文字形式。



#### **3. 页面模块详细需求**



**3.1 M1: 任务与审批管理 (行政流程科技化)**

- **3.1.1 任务看板页**
  - **页面目标：** 让用户宏观了解所有任务状态。
  - **布局与组件：**
    - 页面顶部是关键指标统计（待审批任务数、进行中任务数、今日完成数），使用翻牌器数字动效。
    - 下方是任务列表，可切换“卡片视图”和“列表视图”。
    - **卡片视图：** 每个任务是一个小的“数据视窗”，显示案件名称、目标号码、申请人、当前状态（用不同颜色的标签或光环表示，如“待审批”为黄色，“进行中”为蓝色，“已驳回”为红色）。
    - **列表视图：** 使用上述定义的“数据表格”组件。
  - **交互逻辑：** 点击任一任务，弹出模态框显示任务详情。提供搜索框，可按案件编号或目标号码进行实时过滤。
- **3.1.2 任务申请/审批详情页**
  - **页面目标：** 处理具体的申请与审批工作。
  - **布局与组件：**
    - 采用多列布局。左侧为申请表单信息只读展示区。右侧为审批操作区。
    - 审批操作区的核心是“高拍仪+OCR”模块：一个“开始扫描”按钮，点击后调用摄像头，扫描区域有动态扫描线效果。下方显示OCR识别出的签字与系统预存签名的比对结果（如“相似度98% - 通过”）。
    - 底部是“批准”和“驳回”两个主按钮。
  - **交互逻辑：** “批准”后任务状态流转，并有成功的动效提示。“驳回”则会弹出一个模态框，要求必须填写驳回理由。

**3.2 M2: 情报分析中心 (核心数据可视化)**

- **页面目标：** 打造核心作战指挥室，进行所有的数据分析与情报挖掘。
- **布局与组件：**
  - **中央主体 (70%屏幕)：** 2.5D或3D的GIS地图组件。地图为深蓝色海洋、黑色陆地、发光街道线的科技风格。
  - **底部面板：** 时间轴控制器 (Timeline Slider)，用于拖动选择时间，回放历史轨迹。
  - **右侧面板组：** 可折叠/切换的数据分析面板。
    - **面板1: 目标信息：** 显示当前选中目标的基本信息。
    - **面板2: 实时轨迹参数：** 显示速度、方向、经纬度等。
    - **面板3: 分析结果：** 显示停留点列表、伴随人员列表等。
- **交互逻辑与数据可视化：**
  - **轨迹回放：** 目标在地图上以带有光晕和尾迹的图标移动，移动平滑流畅。
  - **定位点：** 不同来源的定位点用不同形状或颜色的图标表示（GPS用准星，Wi-Fi用扇形，基站用三角）。
  - **智能布控：** 在地图上绘制地理围栏时，区域有半透明颜色填充和动态脉冲边框。触发告警时，地图上相应位置出现闪烁的告警图标，并伴有音效。
  - **图表联动：** 点击地图上的停留点，右侧分析面板自动更新并高亮显示该点的详细信息。



#### **4. 动效与微交互**



- **页面加载：** 首次进入系统时，应有科技感的加载动画（如多个几何图形组合成Logo）。
- **数据更新：** 面板上的数字变化时，应有平滑的滚动动画，而不是瞬间跳变。
- **悬浮效果 (Hover):** 所有可交互的元素（按钮、列表项、地图上的点）在鼠标悬浮时都应有视觉反馈（如发光、放大、边框变亮）。
- **过渡动画：** 页面和面板之间的切换使用淡入淡出或平滑位移的动画，避免生硬的跳转。



#### **5. 技术栈与框架建议**



- **前端框架：** `Vue 3` 或 `React 18`，利用其强大的组件化和状态管理能力。
- **UI 组件库：** 建议不直接使用 `Element Plus` 或 `Ant Design` 的原生样式，而是基于其逻辑，进行深度样式定制。或使用 `Headless UI` 组件库，以获得最大的样式控制自由。
- **数据可视化：** `ECharts`。其对深色主题和科技感图表的支持非常成熟，能很好地实现所需效果。
- **GIS地图引擎：** `Mapbox GL JS` 或 `CesiumJS`。两者都支持高度自定义的地图样式和3D效果，能完美打造科技感的地图背景。
- **动画库：** `GSAP` (GreenSock Animation Platform) 或 `Framer Motion`，用于实现复杂的、高性能的动画效果。

