# 基础数据安全应用平台 - 系统原型图

## 1. 全局布局原型

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│ 顶部信息栏 (Header Bar) - 深蓝色背景 #021028                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ [Logo] 基础数据安全应用平台    │    2024-01-15 14:30:25    │  🔔(3) 👤张三 [退出]    │
└─────────────────────────────────────────────────────────────────────────────────────┘
┌──────────────┬─────────────────────────────────────────────────────────────────────────┐
│ 左侧导航菜单   │                     主内容区                                            │
│ (可收缩)      │                                                                        │
│              │                                                                        │
│ 📊 任务看板   │                                                                        │
│ 🎯 情报中心   │                                                                        │
│ 📈 统计报表   │                                                                        │
│ ⚙️ 系统管理   │                                                                        │
│              │                                                                        │
│              │                                                                        │
│              │                                                                        │
│              │                                                                        │
└──────────────┴─────────────────────────────────────────────────────────────────────────┘
```

## 2. 任务看板页面原型

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              任务看板 - 关键指标统计                                  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   待审批任务     │  │   进行中任务     │  │   今日完成      │  │   总任务数      │ │
│  │      [23]       │  │      [15]       │  │      [8]        │  │     [156]       │ │
│  │   (翻牌器动效)   │  │   (翻牌器动效)   │  │   (翻牌器动效)   │  │  (翻牌器动效)   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ 🔍 搜索框: [按案件编号或目标号码搜索...]     [卡片视图] [列表视图]                      │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                任务列表区域                                          │
│                                                                                     │
│ 卡片视图:                                                                           │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐    │
│ │ 案件: 张某监控   │ │ 案件: 李某调查   │ │ 案件: 王某追踪   │ │ 案件: 赵某分析   │    │
│ │ 目标: 138****   │ │ 目标: 139****   │ │ 目标: 137****   │ │ 目标: 136****   │    │
│ │ 申请人: 刘警官   │ │ 申请人: 陈警官   │ │ 申请人: 李警官   │ │ 申请人: 王警官   │    │
│ │ 状态: [待审批]   │ │ 状态: [进行中]   │ │ 状态: [已驳回]   │ │ 状态: [待审批]   │    │
│ │    (黄色光环)    │ │    (蓝色光环)    │ │    (红色光环)    │ │    (黄色光环)    │    │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘ └─────────────────┘    │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 3. 任务审批详情页面原型

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              任务审批详情                                            │
├─────────────────────────────────────────────────────────────────────────────────────┤
│ 左侧申请信息区域 (60%)                │  右侧审批操作区域 (40%)                      │
│                                      │                                              │
│ ┌─────────────────────────────────┐  │  ┌─────────────────────────────────────┐   │
│ │ 申请表单信息 (只读)              │  │  │ 高拍仪+OCR模块                       │   │
│ │                                 │  │  │                                     │   │
│ │ 案件编号: CASE-2024-001         │  │  │ ┌─────────────────────────────────┐ │   │
│ │ 目标号码: 138****8888           │  │  │ │     📷 摄像头扫描区域            │ │   │
│ │ 申请人: 张警官                  │  │  │ │                                 │ │   │
│ │ 申请时间: 2024-01-15 10:30     │  │  │ │    [动态扫描线效果]              │ │   │
│ │ 申请理由: 涉嫌诈骗案件调查      │  │  │ │                                 │ │   │
│ │ 监控时长: 30天                  │  │  │ └─────────────────────────────────┘ │   │
│ │ ...                             │  │  │                                     │   │
│ └─────────────────────────────────┘  │  │ [开始扫描] 按钮                     │   │
│                                      │  │                                     │   │
│                                      │  │ OCR识别结果:                        │   │
│                                      │  │ 签名比对: 相似度 98% - ✅ 通过      │   │
│                                      │  └─────────────────────────────────────┘   │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                          底部操作按钮区域                                            │
│                    [批准] (科技蓝)        [驳回] (橙黄色)                           │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 4. 情报分析中心页面原型

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              情报分析中心                                            │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                   右侧分析面板组     │
│                                                                 ┌─────────────────┐ │
│                                                                 │ 📍 目标信息      │ │
│                     中央GIS地图区域 (70%)                        │                 │ │
│                                                                 │ 姓名: 张某       │ │
│                   ┌─────────────────────────────┐               │ 号码: 138****   │ │
│                   │                             │               │ 状态: 在线      │ │
│                   │    🗺️ 2.5D/3D科技风地图     │               └─────────────────┘ │
│                   │                             │               ┌─────────────────┐ │
│                   │  📍 定位点 (GPS/WiFi/基站)  │               │ 📊 实时轨迹参数  │ │
│                   │                             │               │                 │ │
│                   │  🎯 目标轨迹 (带光晕尾迹)   │               │ 速度: 45km/h    │ │
│                   │                             │               │ 方向: 东北      │ │
│                   │  ⚠️ 告警区域 (脉冲边框)     │               │ 经度: 116.404   │ │
│                   │                             │               │ 纬度: 39.915    │ │
│                   └─────────────────────────────┘               └─────────────────┘ │
│                                                                 ┌─────────────────┐ │
│                                                                 │ 🔍 分析结果      │ │
│                                                                 │                 │ │
│                                                                 │ 停留点: 3个     │ │
│                                                                 │ 伴随人员: 2人   │ │
│                                                                 │ 异常行为: 无    │ │
│                                                                 └─────────────────┘ │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                          底部时间轴控制器                                            │
│ ⏮️ ⏸️ ▶️ ⏭️    ├─────●─────────────────────────────────────────┤  2024-01-15 14:30 │
│                    08:00                                    18:00                   │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 5. 核心组件样式规范

### 5.1 数据视窗/面板
```
┌─────────────────────────────────────┐
│ 📊 面板标题                          │ ← 发光边框 #00E5FF
├─────────────────────────────────────┤
│                                     │
│        面板内容区域                  │ ← 半透明背景 rgba(5,25,60,0.8)
│                                     │
│                                     │
└─────────────────────────────────────┘
```

### 5.2 主按钮样式
```
┌─────────────────┐
│   [确认操作]     │ ← 科技蓝背景 #0A72FF
└─────────────────┘   悬浮时光晕扩散效果
```

### 5.3 表单元素
```
输入框: _________________ ← 无背景，底边框线，焦点时发光
下拉框: [选择项目 ▼] _____ ← 同上样式
```

## 6. 色彩应用示例

- **主背景**: #021028 (深邃藏蓝)
- **面板背景**: rgba(5,25,60,0.8) (半透明深蓝)
- **边框/强调**: #00E5FF (明亮青色) / #0A72FF (科技蓝)
- **告警/高亮**: #FFB400 (橙黄) / #FF4D4F (红色)
- **文字**: #A6C8FF (淡蓝) / #FFFFFF (白色)
- **状态标签**:
  - 待审批: #FFB400 (黄色光环)
  - 进行中: #0A72FF (蓝色光环)  
  - 已驳回: #FF4D4F (红色光环)
  - 已完成: #52C41A (绿色光环)


graph TB
    %% 主界面布局
    subgraph "基础数据安全应用平台"
        Header["🏢 顶部信息栏<br/>Logo | 2024-01-15 14:30:25 | 🔔(3) 👤张三 [退出]"]
        
        subgraph LeftNav["📋 左侧导航菜单"]
            TaskBoard["📊 任务看板"]
            IntelCenter["🎯 情报中心"] 
            Statistics["📈 统计报表"]
            SystemMgmt["⚙️ 系统管理"]
        end
        
        subgraph MainContent["主内容区域"]
            %% 任务看板页面
            subgraph TaskDashboard["任务看板页面"]
                subgraph KPICards["关键指标统计"]
                    PendingApproval["待审批任务<br/>[23]<br/>(翻牌器动效)"]
                    InProgress["进行中任务<br/>[15]<br/>(翻牌器动效)"]
                    TodayCompleted["今日完成<br/>[8]<br/>(翻牌器动效)"]
                    TotalTasks["总任务数<br/>[156]<br/>(翻牌器动效)"]
                end
                
                SearchBar["🔍 搜索框: 按案件编号或目标号码搜索... | [卡片视图] [列表视图]"]
                
                subgraph TaskCards["任务卡片列表"]
                    Card1["案件: 张某监控<br/>目标: 138****<br/>申请人: 刘警官<br/>状态: [待审批] (黄色光环)"]
                    Card2["案件: 李某调查<br/>目标: 139****<br/>申请人: 陈警官<br/>状态: [进行中] (蓝色光环)"]
                    Card3["案件: 王某追踪<br/>目标: 137****<br/>申请人: 李警官<br/>状态: [已驳回] (红色光环)"]
                    Card4["案件: 赵某分析<br/>目标: 136****<br/>申请人: 王警官<br/>状态: [待审批] (黄色光环)"]
                end
            end
            
            %% 任务审批详情页面
            subgraph ApprovalDetail["任务审批详情页面"]
                subgraph LeftPanel["左侧申请信息区域 (60%)"]
                    ApplicationForm["申请表单信息 (只读)<br/>案件编号: CASE-2024-001<br/>目标号码: 138****8888<br/>申请人: 张警官<br/>申请时间: 2024-01-15 10:30<br/>申请理由: 涉嫌诈骗案件调查<br/>监控时长: 30天"]
                end
                
                subgraph RightPanel["右侧审批操作区域 (40%)"]
                    OCRModule["高拍仪+OCR模块<br/>📷 摄像头扫描区域<br/>[动态扫描线效果]<br/>[开始扫描] 按钮<br/>OCR识别结果:<br/>签名比对: 相似度 98% - ✅ 通过"]
                end
                
                ActionButtons["底部操作按钮区域<br/>[批准] (科技蓝) | [驳回] (橙黄色)"]
            end
            
            %% 情报分析中心页面
            subgraph IntelAnalysis["情报分析中心页面"]
                subgraph GISMap["中央GIS地图区域 (70%)"]
                    MapArea["🗺️ 2.5D/3D科技风地图<br/>📍 定位点 (GPS/WiFi/基站)<br/>🎯 目标轨迹 (带光晕尾迹)<br/>⚠️ 告警区域 (脉冲边框)"]
                end
                
                subgraph RightAnalysisPanel["右侧分析面板组"]
                    TargetInfo["📍 目标信息<br/>姓名: 张某<br/>号码: 138****<br/>状态: 在线"]
                    RealtimeParams["📊 实时轨迹参数<br/>速度: 45km/h<br/>方向: 东北<br/>经度: 116.404<br/>纬度: 39.915"]
                    AnalysisResult["🔍 分析结果<br/>停留点: 3个<br/>伴随人员: 2人<br/>异常行为: 无"]
                end
                
                TimelineControl["底部时间轴控制器<br/>⏮️ ⏸️ ▶️ ⏭️ | 08:00 ●─────────────── 18:00 | 2024-01-15 14:30"]
            end
        end
    end
    
    %% 连接关系
    Header --> LeftNav
    Header --> MainContent
    LeftNav --> MainContent
    
    TaskBoard --> TaskDashboard
    IntelCenter --> IntelAnalysis
    
    %% 任务看板内部连接
    KPICards --> SearchBar
    SearchBar --> TaskCards
    
    %% 审批详情内部连接
    LeftPanel --> ActionButtons
    RightPanel --> ActionButtons
    
    %% 情报中心内部连接
    GISMap --> RightAnalysisPanel
    GISMap --> TimelineControl
    
    %% 页面跳转关系
    Card1 -.-> ApprovalDetail
    Card2 -.-> ApprovalDetail
    Card4 -.-> ApprovalDetail
    
    %% 样式定义
    classDef headerStyle fill:#021028,stroke:#00E5FF,stroke-width:2px,color:#FFFFFF
    classDef navStyle fill:#051960,stroke:#0A72FF,stroke-width:2px,color:#A6C8FF
    classDef contentStyle fill:#051960,stroke:#00E5FF,stroke-width:1px,color:#A6C8FF
    classDef cardStyle fill:#051960,stroke:#FFB400,stroke-width:2px,color:#FFFFFF
    classDef approvalStyle fill:#051960,stroke:#0A72FF,stroke-width:2px,color:#A6C8FF
    classDef mapStyle fill:#021028,stroke:#00E5FF,stroke-width:3px,color:#A6C8FF
    classDef panelStyle fill:#051960,stroke:#00E5FF,stroke-width:1px,color:#A6C8FF
    
    class Header headerStyle
    class LeftNav,TaskBoard,IntelCenter,Statistics,SystemMgmt navStyle
    class MainContent,TaskDashboard,SearchBar contentStyle
    class Card1,Card2,Card3,Card4 cardStyle
    class ApprovalDetail,LeftPanel,RightPanel,ActionButtons approvalStyle
    class GISMap,MapArea mapStyle
    class TargetInfo,RealtimeParams,AnalysisResult,TimelineControl panelStyle
